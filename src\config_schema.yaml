# Configuration options for the Whisper models
model_options:
  use_api:
    value: false
    type: bool
    description: "Toggle to choose whether to use the OpenAI API or a local Whisper model for transcription."

  # Common configuration options for both API and local models
  common:
    language:
      value: null
      type: str
      description: "The language code for the transcription in ISO-639-1 format."
    temperature:
      value: 0.0
      type: float
      description: "Controls the randomness of the transcription output. Lower values make the output more focused and deterministic."
    initial_prompt:
      value: null
      type: str
      description: "A string used as an initial prompt to condition the transcription. More info: https://platform.openai.com/docs/guides/speech-to-text/prompting"

  # Configuration options for the OpenAI API
  api:
    model:
      value: whisper-1
      type: str
      description: "The model to use for transcription. Currently only 'whisper-1' is available."
    base_url:
      value: https://api.openai.com/v1
      type: str
      description: "The base URL for the API. Can be changed to use a local API endpoint."
    api_key:
      value: null
      type: str
      description: "Your API key for the OpenAI API. Required for non-local API usage."

  # Configuration options for the faster-whisper model
  local:
    model:
      value: base
      type: str
      description: "The model to use for transcription. The larger models provide better accuracy but are slower."
      options:
        - base
        - base.en
        - tiny
        - tiny.en
        - small
        - small.en
        - medium
        - medium.en
        - large
        - large-v1
        - large-v2
        - large-v3
    device:
      value: auto
      type: str
      description: "The device to run the local Whisper model on. Use 'cuda' for NVIDIA GPUs, 'cpu' for CPU-only processing, or 'auto' to let the system automatically choose the best available device."
      options:
        - auto
        - cuda
        - cpu
    compute_type:
      value: default
      type: str
      description: "The compute type to use for the local Whisper model."
      options:
        - default
        - float32
        - float16
        - int8
    condition_on_previous_text:
      value: true
      type: bool
      description: "Set to true to use the previously transcribed text as a prompt for the next transcription request."
    vad_filter:
      value: false
      type: bool
      description: "Set to true to use a voice activity detection (VAD) filter to remove silence from the recording."
    model_path:
      value: null
      type: str
      description: "The path to the local Whisper model. If not specified, the default model will be downloaded."

# Configuration options for activation and recording
recording_options:
  activation_key:
    value: ctrl+shift+space
    type: str
    description: "The keyboard shortcut to activate the recording and transcribing process. Separate keys with a '+'."
  input_backend:
    value: auto
    type: str
    description: "The input backend to use for detecting key presses. 'auto' will try to use the best available backend."
    options:
      - auto
      - evdev
      - pynput
  recording_mode:
    value: continuous
    type: str
    description: "The recording mode to use. Options include continuous (auto-restart recording after pause in speech until activation key is pressed again), voice_activity_detection (stop recording after pause in speech), press_to_toggle (stop recording when activation key is pressed again), hold_to_record (stop recording when activation key is released)."
    options:
      - continuous
      - voice_activity_detection
      - press_to_toggle
      - hold_to_record
  sound_device:
    value: null
    type: str
    description: "The numeric index of the sound device to use for recording. To find device numbers, run `python -m sounddevice`"
  sample_rate:
    value: 16000
    type: int
    description: "The sample rate in Hz to use for recording."
  silence_duration:
    value: 900
    type: int
    description: "The duration in milliseconds to wait for silence before stopping the recording."
  min_duration:
    value: 100
    type: int
    description: "The minimum duration in milliseconds for a recording to be processed. Recordings shorter than this will be discarded."

# Post-processing options for the transcribed text
post_processing:
  writing_key_press_delay:
    value: 0.005
    type: float
    description: "The delay in seconds between each key press when writing the transcribed text."
  remove_trailing_period:
    value: false
    type: bool
    description: "Set to true to remove the trailing period from the transcribed text."
  add_trailing_space:
    value: true
    type: bool
    description: "Set to true to add a space to the end of the transcribed text."
  remove_capitalization:
    value: false
    type: bool
    description: "Set to true to convert the transcribed text to lowercase."
  input_method:
    value: pynput
    type: str
    description: "The method to use for simulating keyboard input."
    options:
      - pynput
      - ydotool
      - dotool

# Miscellaneous settings
misc:
  print_to_terminal:
    value: true
    type: bool
    description: "Set to true to print the script status and transcribed text to the terminal."
  hide_status_window:
    value: false
    type: bool
    description: "Set to true to hide the status window during operation."
  noise_on_completion:
    value: false
    type: bool
    description: "Set to true to play a noise after the transcription has been typed out."
