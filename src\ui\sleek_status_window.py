import sys
import os
import json
from datetime import datetime
from PyQt5.QtCore import (Qt, pyqtSignal, pyqtSlot, QTimer, QPropertyAnimation, 
                         QEasingCurve, QPoint, QSize)
from PyQt5.QtGui import (QFont, QPainter, QBrush, QColor, QPen, QLinearGradient, 
                        QFontMetrics, QCursor)
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget, 
                           QPushButton, QGraphicsDropShadowEffect, QMainWindow)

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils import ConfigManager


class SleekVoiceMeter(QWidget):
    """Horizontal voice meter like the reference image."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(150, 20)
        self.levels = [0] * 15
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(50)
        
    def set_level(self, level):
        """Set audio level - FIXED: Green=Low, Red=High."""
        target_bars = int((level / 100.0) * len(self.levels))
        
        for i in range(len(self.levels)):
            if i < target_bars:
                self.levels[i] = min(100, level)
            else:
                self.levels[i] = max(0, self.levels[i] - 20)
                
    def update_animation(self):
        for i in range(len(self.levels)):
            if self.levels[i] > 0:
                self.levels[i] = max(0, self.levels[i] - 5)
        self.update()
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        bar_width = 2
        bar_spacing = 1
        start_x = 5
        
        for i, level in enumerate(self.levels):
            if level > 3:
                x = start_x + i * (bar_width + bar_spacing)
                bar_height = int((level / 100.0) * (self.height() - 4))
                y = (self.height() - bar_height) // 2
                
                # CORRECT: Green for low (quiet), Red for high (loud)
                if i < len(self.levels) * 0.4:  # Low levels = Green
                    color = QColor(34, 197, 94, 200)
                elif i < len(self.levels) * 0.7:  # Medium = Yellow
                    color = QColor(251, 191, 36, 200)
                else:  # High levels = Red
                    color = QColor(239, 68, 68, 200)
                    
                painter.setBrush(QBrush(color))
                painter.setPen(Qt.NoPen)
                painter.drawRoundedRect(x, y, bar_width, bar_height, 1, 1)


class CircularArrowIndicator(QWidget):
    """Circular arrow indicator that appears on hover."""
    
    clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(40, 40)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setCursor(QCursor(Qt.PointingHandCursor))
        
        # Add glow effect
        glow = QGraphicsDropShadowEffect()
        glow.setBlurRadius(20)
        glow.setColor(QColor(59, 130, 246, 150))
        glow.setOffset(0, 0)
        self.setGraphicsEffect(glow)
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw circular background
        painter.setBrush(QBrush(QColor(59, 130, 246, 220)))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(5, 5, 30, 30)
        
        # Draw arrow
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.drawText(12, 25, "◀")
        
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit()


class SleekStatusWindow(QWidget):
    """Sleek horizontal status bar like the reference image."""
    
    statusSignal = pyqtSignal(str)
    closeSignal = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.is_hidden = True
        self.is_recording = False
        self.recording_history = []
        self.history_file = os.path.join('src', 'recording_history.json')
        
        self.init_sleek_ui()
        self.load_history()
        self.setup_animations()
        self.create_arrow_indicator()
        self.position_window()
        
    def init_sleek_ui(self):
        """Initialize sleek horizontal UI like reference image."""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setFixedSize(600, 60)
        
        # Main container with sleek styling
        self.main_container = QWidget()
        self.main_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 30, 35, 0.95),
                    stop:1 rgba(20, 20, 25, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 30px;
            }
        """)
        
        # Add shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 5)
        self.main_container.setGraphicsEffect(shadow)
        
        # Horizontal layout for status bar
        main_layout = QHBoxLayout(self.main_container)
        main_layout.setContentsMargins(20, 15, 20, 15)
        main_layout.setSpacing(20)
        
        # Status section
        self.create_status_section(main_layout)
        
        # Voice meter
        self.voice_meter = SleekVoiceMeter()
        main_layout.addWidget(self.voice_meter)
        
        # Action buttons
        self.create_action_buttons(main_layout)
        
        # Set main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.main_container)
        
    def create_status_section(self, layout):
        """Create status section like reference."""
        status_layout = QHBoxLayout()
        
        # Status dot
        self.status_dot = QLabel("●")
        self.status_dot.setFont(QFont('Segoe UI', 12))
        self.status_dot.setStyleSheet("color: #10b981;")  # Green
        
        # Status text
        self.status_label = QLabel('Ready')
        self.status_label.setFont(QFont('Segoe UI', 11, QFont.Medium))
        self.status_label.setStyleSheet("color: white;")
        
        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_label)
        layout.addLayout(status_layout)
        
    def create_action_buttons(self, layout):
        """Create action buttons like reference."""
        layout.addStretch()
        
        # History button
        history_btn = QPushButton("History")
        history_btn.setStyleSheet(self.get_button_style())
        history_btn.clicked.connect(self.open_history_window)
        layout.addWidget(history_btn)
        
        # Settings button  
        settings_btn = QPushButton("Settings")
        settings_btn.setStyleSheet(self.get_button_style())
        settings_btn.clicked.connect(self.open_settings)
        layout.addWidget(settings_btn)
        
        # Close button
        close_btn = QPushButton("✕")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background: rgba(239, 68, 68, 0.8);
                border: none;
                border-radius: 15px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(239, 68, 68, 1.0);
            }
        """)
        close_btn.clicked.connect(self.slide_out)
        layout.addWidget(close_btn)
        
    def get_button_style(self):
        """Get consistent button styling."""
        return """
            QPushButton {
                background: rgba(59, 130, 246, 0.8);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                padding: 8px 16px;
                font-size: 11px;
            }
            QPushButton:hover {
                background: rgba(59, 130, 246, 1.0);
            }
            QPushButton:pressed {
                background: rgba(37, 99, 235, 0.9);
            }
        """
        
    def create_arrow_indicator(self):
        """Create circular arrow indicator."""
        self.arrow_indicator = CircularArrowIndicator()
        self.arrow_indicator.clicked.connect(self.slide_in)
        
    def setup_animations(self):
        """Setup slide animations."""
        self.slide_animation = QPropertyAnimation(self, b"pos")
        self.slide_animation.setDuration(400)
        self.slide_animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def position_window(self):
        """Position window and arrow."""
        screen = QApplication.primaryScreen().geometry()
        
        # Position main window (completely hidden)
        self.hidden_x = screen.width()
        self.visible_x = screen.width() - self.width() - 20
        self.y_pos = 50  # Top area like reference
        
        self.move(self.hidden_x, self.y_pos)
        self.show()
        
        # Position arrow (hidden initially)
        arrow_x = screen.width() - 20
        arrow_y = self.y_pos + 10
        self.arrow_indicator.move(arrow_x, arrow_y)
        self.arrow_indicator.hide()
        
        # Create hover detection area
        self.create_hover_area()
        
    def create_hover_area(self):
        """Create invisible hover area at screen edge."""
        self.hover_area = QWidget()
        self.hover_area.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.hover_area.setAttribute(Qt.WA_TranslucentBackground, True)
        self.hover_area.setFixedSize(10, 100)
        
        screen = QApplication.primaryScreen().geometry()
        self.hover_area.move(screen.width() - 10, self.y_pos - 20)
        self.hover_area.show()
        
        # Connect hover events
        self.hover_area.enterEvent = lambda event: self.show_arrow()
        self.hover_area.leaveEvent = lambda event: self.hide_arrow_delayed()
        
        # Timer for delayed arrow hiding
        self.arrow_hide_timer = QTimer()
        self.arrow_hide_timer.setSingleShot(True)
        self.arrow_hide_timer.timeout.connect(self.hide_arrow)
        
    def show_arrow(self):
        """Show arrow indicator."""
        self.arrow_hide_timer.stop()
        self.arrow_indicator.show()
        
    def hide_arrow_delayed(self):
        """Hide arrow after delay."""
        self.arrow_hide_timer.start(1000)
        
    def hide_arrow(self):
        """Hide arrow indicator."""
        if not self.arrow_indicator.underMouse():
            self.arrow_indicator.hide()

    def slide_in(self):
        """Slide window into view."""
        self.is_hidden = False
        self.arrow_indicator.hide()

        self.slide_animation.setStartValue(QPoint(self.hidden_x, self.y_pos))
        self.slide_animation.setEndValue(QPoint(self.visible_x, self.y_pos))
        self.slide_animation.start()

    def slide_out(self):
        """Slide window out of view."""
        self.is_hidden = True

        self.slide_animation.setStartValue(QPoint(self.visible_x, self.y_pos))
        self.slide_animation.setEndValue(QPoint(self.hidden_x, self.y_pos))
        self.slide_animation.start()

    def update_status(self, status, is_recording=False):
        """Update status display."""
        self.is_recording = is_recording
        self.status_label.setText(status)

        if is_recording:
            self.status_dot.setStyleSheet("color: #ef4444;")  # Red
        else:
            self.status_dot.setStyleSheet("color: #10b981;")  # Green

        # Auto slide in when recording starts
        if is_recording and self.is_hidden:
            self.slide_in()

    def update_voice_level(self, level):
        """Update voice level meter."""
        self.voice_meter.set_level(level)

    def open_history_window(self):
        """Open the history management window."""
        from .history_window import HistoryWindow
        self.history_window = HistoryWindow(self.recording_history)
        self.history_window.show()

    def open_settings(self):
        """Open settings window."""
        # TODO: Implement settings window
        pass

    def load_history(self):
        """Load recording history."""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.recording_history = json.load(f)
        except Exception as e:
            print(f"Error loading history: {e}")
            self.recording_history = []

    def save_history(self):
        """Save recording history."""
        try:
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.recording_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving history: {e}")

    def add_recording(self, text, duration, timestamp=None):
        """Add new recording to history."""
        if timestamp is None:
            timestamp = datetime.now().isoformat()

        recording = {
            'text': text,
            'duration': duration,
            'timestamp': timestamp,
            'id': len(self.recording_history)
        }

        self.recording_history.insert(0, recording)  # Add to beginning
        self.save_history()

    def closeEvent(self, event):
        """Handle close event."""
        self.arrow_indicator.close()
        self.hover_area.close()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SleekStatusWindow()
    window.show()

    # Test the voice meter
    import random
    timer = QTimer()
    def update_level():
        level = random.randint(0, 100)
        window.update_voice_level(level)
        window.update_status(f"Recording... {level}%", True)

    timer.timeout.connect(update_level)
    timer.start(100)

    sys.exit(app.exec_())
